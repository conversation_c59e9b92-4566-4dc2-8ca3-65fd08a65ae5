/**
 * ConversationFlowAnalyzer 模块化接口定义
 *
 * 本文件定义了重构后各模块间的清晰接口，确保低耦合度和高内聚性
 */

import { ChatMessage } from "../../shared/database";

// ==================== 核心数据接口 ====================

/**
 * 分析上下文
 */
export interface AnalysisContext {
    channelId: string;
    message: ChatMessage;
    history: MessageHistory;
    userProfiles: Map<string, UserProfile>;
    timestamp: Date;
}

/**
 * 消息历史
 */
export interface MessageHistory {
    recentMessages: ChatMessage[];
    messageCount: number;
    timeSpan: number; // 时间跨度（毫秒）
}

/**
 * 用户画像
 */
export interface UserProfile {
    userId: string;
    messageCount: number;
    lastActiveTime: Date;
    averageInterval: number;
    participationScore: number;
    isActiveParticipant: boolean;
    interests: string[];
    communicationStyle: 'active' | 'passive' | 'lurker';
}

/**
 * 分析结果基类
 */
export interface AnalysisResult {
    confidence: number;
    reasoning: string;
    timestamp: Date;
    metadata: Record<string, any>;
}

// ==================== 话题分析接口 ====================

/**
 * 话题信息
 */
export interface TopicInfo {
    topicId: string;
    status: 'developing' | 'stable' | 'cooling' | 'ended';
    keywords: string[];
    participants: Set<string>;
    dominantParticipants: string[];
    messageCount: number;
    lastActivity: Date;
    stability: number;
    engagementLevel: number;
    coherence: number;
    semanticVector?: number[]; // embedding向量
}

/**
 * 话题分析结果
 */
export interface TopicAnalysisResult extends AnalysisResult {
    activeTopics: TopicInfo[];
    newTopics: TopicInfo[];
    endedTopics: string[];
    topicTransitions: TopicTransition[];
    overallCoherence: number;
}

/**
 * 话题转换
 */
export interface TopicTransition {
    fromTopicId?: string;
    toTopicId: string;
    transitionType: 'continuation' | 'shift' | 'split' | 'merge' | 'new';
    confidence: number;
}

/**
 * 话题更新
 */
export interface TopicUpdate {
    keywords?: string[];
    status?: TopicInfo['status'];
    participants?: Set<string>;
    metadata?: Record<string, any>;
}

/**
 * 话题分析器接口
 */
export interface ITopicAnalyzer {
    /**
     * 分析话题
     */
    analyzeTopics(context: AnalysisContext): Promise<TopicAnalysisResult>;

    /**
     * 更新话题状态
     */
    updateTopicState(topicId: string, update: TopicUpdate): void;

    /**
     * 获取活跃话题
     */
    getActiveTopics(channelId: string): TopicInfo[];

    /**
     * 计算话题相似度
     */
    calculateTopicSimilarity(topic1: TopicInfo, topic2: TopicInfo): Promise<number>;

    /**
     * 合并相似话题
     */
    mergeSimilarTopics(channelId: string, threshold: number): Promise<void>;
}

// ==================== 用户活跃度分析接口 ====================

/**
 * 频道动态
 */
export interface ChannelDynamics {
    totalParticipants: number;
    activeParticipants: number;
    conversationIntensity: number;
    topicSwitchFrequency: number;
    averageResponseTime: number;
    peakActivityPeriods: TimeRange[];
}

/**
 * 时间范围
 */
export interface TimeRange {
    start: Date;
    end: Date;
    intensity: number;
}

/**
 * 活跃度分析结果
 */
export interface ActivityAnalysisResult extends AnalysisResult {
    userProfile: UserProfile;
    channelDynamics: ChannelDynamics;
    participationTrend: 'increasing' | 'stable' | 'decreasing';
    influenceScore: number;
    engagementPrediction: number;
}

/**
 * 用户活跃度跟踪器接口
 */
export interface IUserActivityTracker {
    /**
     * 跟踪用户活跃度
     */
    trackActivity(context: AnalysisContext): Promise<ActivityAnalysisResult>;

    /**
     * 获取用户画像
     */
    getUserProfile(userId: string): UserProfile | null;

    /**
     * 获取频道动态
     */
    getChannelDynamics(channelId: string): ChannelDynamics;

    /**
     * 预测用户参与度
     */
    predictEngagement(userId: string, channelId: string): Promise<number>;

    /**
     * 识别影响力用户
     */
    identifyInfluencers(channelId: string): string[];
}

// ==================== 节奏分析接口 ====================

/**
 * 节奏分析结果
 */
export interface PaceAnalysisResult extends AnalysisResult {
    currentPace: 'fast' | 'normal' | 'slow';
    paceStability: number;
    burstDetected: boolean;
    optimalResponseWindow: TimeWindow;
    paceHistory: PaceSnapshot[];
}

/**
 * 时间窗口
 */
export interface TimeWindow {
    start: Date;
    end: Date;
    confidence: number;
}

/**
 * 节奏快照
 */
export interface PaceSnapshot {
    timestamp: Date;
    pace: 'fast' | 'normal' | 'slow';
    messageCount: number;
    averageInterval: number;
}

/**
 * 时机预测
 */
export interface TimingPrediction {
    suggestedWaitTime: number;
    confidence: number;
    reasoning: string;
    alternativeTimings: number[];
}

/**
 * 节奏分析器接口
 */
export interface IPaceAnalyzer {
    /**
     * 分析对话节奏
     */
    analyzePace(context: AnalysisContext): Promise<PaceAnalysisResult>;

    /**
     * 预测最佳时机
     */
    predictOptimalTiming(channelId: string): Promise<TimingPrediction>;

    /**
     * 检测消息爆发
     */
    detectBurst(messages: ChatMessage[]): boolean;

    /**
     * 计算节奏稳定性
     */
    calculatePaceStability(channelId: string): number;
}

// ==================== 状态管理接口 ====================

/**
 * 对话状态
 */
export interface ConversationState {
    channelId: string;
    topics: Map<string, TopicInfo>;
    userProfiles: Map<string, UserProfile>;
    channelDynamics: ChannelDynamics;
    lastUpdated: Date;
    version: number;
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
    memoryUsage: {
        totalSize: number;
        topicsSize: number;
        usersSize: number;
        messagesSize: number;
    };
    operationStats: {
        analysisCount: number;
        averageAnalysisTime: number;
        cacheHitRate: number;
        errorRate: number;
    };
    resourceUtilization: {
        cpuUsage: number;
        memoryUsage: number;
        diskUsage: number;
    };
}

/**
 * 状态管理器接口
 */
export interface IStateManager {
    /**
     * 保存状态
     */
    saveState(channelId: string, state: ConversationState): Promise<void>;

    /**
     * 加载状态
     */
    loadState(channelId: string): Promise<ConversationState | null>;

    /**
     * 优化内存
     */
    optimizeMemory(): Promise<void>;

    /**
     * 获取性能指标
     */
    getMetrics(): PerformanceMetrics;

    /**
     * 清理过期数据
     */
    cleanup(maxAge: number): Promise<void>;

    /**
     * 创建状态快照
     */
    createSnapshot(channelId: string): Promise<string>;

    /**
     * 恢复状态快照
     */
    restoreSnapshot(channelId: string, snapshotId: string): Promise<void>;
}

// ==================== 决策引擎接口 ====================

/**
 * 回复决策
 */
export interface ReplyDecision {
    shouldReply: boolean;
    confidence: number;
    reasoning: string;
    suggestedWaitTime: number;
    priority: 'high' | 'medium' | 'low';
    tags: string[];
}

/**
 * 决策解释
 */
export interface DecisionExplanation {
    factors: DecisionFactor[];
    weights: Record<string, number>;
    finalScore: number;
    threshold: number;
    alternatives: AlternativeDecision[];
}

/**
 * 决策因子
 */
export interface DecisionFactor {
    name: string;
    value: number;
    weight: number;
    impact: 'positive' | 'negative' | 'neutral';
    description: string;
}

/**
 * 替代决策
 */
export interface AlternativeDecision {
    decision: ReplyDecision;
    probability: number;
    conditions: string[];
}

/**
 * 决策引擎接口
 */
export interface IDecisionEngine {
    /**
     * 制定决策
     */
    makeDecision(
        topicAnalysis: TopicAnalysisResult,
        activityAnalysis: ActivityAnalysisResult,
        paceAnalysis: PaceAnalysisResult
    ): Promise<ReplyDecision>;

    /**
     * 解释决策
     */
    explainDecision(decision: ReplyDecision): Promise<DecisionExplanation>;

    /**
     * 更新决策规则
     */
    updateRules(rules: DecisionRule[]): void;

    /**
     * 学习决策模式
     */
    learnFromFeedback(decision: ReplyDecision, feedback: DecisionFeedback): Promise<void>;
}

/**
 * 决策规则
 */
export interface DecisionRule {
    id: string;
    name: string;
    condition: string;
    action: string;
    weight: number;
    enabled: boolean;
}

/**
 * 决策反馈
 */
export interface DecisionFeedback {
    decisionId: string;
    actualOutcome: 'success' | 'failure' | 'neutral';
    userSatisfaction: number;
    contextFactors: Record<string, any>;
    timestamp: Date;
}

// ==================== 智能化服务接口 ====================

/**
 * 语义分析结果
 */
export interface SemanticAnalysis {
    intent: string;
    entities: Entity[];
    sentiment: SentimentScore;
    topics: string[];
    keywords: string[];
    embedding?: number[];
    confidence: number;
}

/**
 * 实体
 */
export interface Entity {
    text: string;
    type: string;
    confidence: number;
    startIndex: number;
    endIndex: number;
}

/**
 * 情感分数
 */
export interface SentimentScore {
    positive: number;
    negative: number;
    neutral: number;
    overall: 'positive' | 'negative' | 'neutral';
}

/**
 * LLM配置
 */
export interface LLMConfig {
    provider: 'openai' | 'local' | 'adapter';
    model: string;
    apiKey?: string;
    endpoint?: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
}

/**
 * NLP配置
 */
export interface NLPConfig {
    language: 'zh' | 'en' | 'auto';
    enableSegmentation: boolean;
    enablePOS: boolean;
    enableNER: boolean;
    enableSentiment: boolean;
}

/**
 * LLM提供商接口
 */
export interface ILLMProvider {
    name: string;

    /**
     * 语义分析
     */
    analyze(text: string, context?: Record<string, any>): Promise<SemanticAnalysis>;

    /**
     * 文本嵌入
     */
    embedText(text: string): Promise<number[]>;

    /**
     * 批量嵌入
     */
    embedTexts(texts: string[]): Promise<number[][]>;

    /**
     * 检查可用性
     */
    isAvailable(): Promise<boolean>;

    /**
     * 获取模型信息
     */
    getModelInfo(): ModelInfo;
}

/**
 * 模型信息
 */
export interface ModelInfo {
    name: string;
    version: string;
    capabilities: string[];
    maxInputLength: number;
    embeddingDimension?: number;
}

/**
 * NLP处理器接口
 */
export interface INLPProcessor {
    /**
     * 文本分词
     */
    tokenize(text: string): string[];

    /**
     * 关键词提取
     */
    extractKeywords(text: string, limit?: number): string[];

    /**
     * 词性标注
     */
    posTag(text: string): Array<{ word: string; pos: string }>;

    /**
     * 命名实体识别
     */
    recognizeEntities(text: string): Entity[];

    /**
     * 情感分析
     */
    analyzeSentiment(text: string): SentimentScore;

    /**
     * 文本相似度
     */
    calculateSimilarity(text1: string, text2: string): number;
}

/**
 * 智能化服务接口
 */
export interface IIntelligenceService {
    /**
     * 初始化服务
     */
    initialize(config: IntelligenceConfig): Promise<void>;

    /**
     * 语义分析
     */
    analyzeSemantics(text: string, context?: AnalysisContext): Promise<SemanticAnalysis>;

    /**
     * 计算文本相似度
     */
    calculateSimilarity(text1: string, text2: string): Promise<number>;

    /**
     * 批量处理
     */
    batchProcess(texts: string[], operation: 'analyze' | 'embed'): Promise<any[]>;

    /**
     * 获取缓存统计
     */
    getCacheStats(): CacheStats;

    /**
     * 清理缓存
     */
    clearCache(): void;
}

/**
 * 智能化配置
 */
export interface IntelligenceConfig {
    llm: LLMConfig;
    nlp: NLPConfig;
    embedding: {
        model: string;
        dimension: number;
        cacheSize: number;
        batchSize: number;
    };
    cache: {
        enabled: boolean;
        maxSize: number;
        ttl: number;
    };
}

/**
 * 缓存统计
 */
export interface CacheStats {
    hitRate: number;
    missRate: number;
    totalRequests: number;
    cacheSize: number;
    memoryUsage: number;
}
