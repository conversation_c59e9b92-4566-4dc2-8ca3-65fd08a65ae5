/**
 * UserActivityTracker - 用户活跃度分析模块
 *
 * 负责用户行为模式分析、参与度评分计算、用户角色识别和活跃度趋势预测
 */

import {
    IUserActivityTracker,
    AnalysisContext,
    ActivityAnalysisResult,
    UserProfile,
    ChannelDynamics,
    TimeRange
} from './interfaces';

/**
 * 用户活跃度跟踪器配置
 */
export interface UserActivityTrackerConfig {
    maxUserProfiles: number;
    userTimeoutMs: number;
    activityWindowMs: number;
    influenceThreshold: number;
    engagementDecayRate: number;
    peakDetectionThreshold: number;
    minMessagesForProfile: number;
    participationScoreWeights: {
        frequency: number;
        recency: number;
        consistency: number;
        influence: number;
    };
}

/**
 * 默认配置
 */
export const DEFAULT_USER_ACTIVITY_TRACKER_CONFIG: UserActivityTrackerConfig = {
    maxUserProfiles: 100,
    userTimeoutMs: 30 * 60 * 1000, // 30分钟
    activityWindowMs: 10 * 60 * 1000, // 10分钟
    influenceThreshold: 0.7,
    engagementDecayRate: 0.95,
    peakDetectionThreshold: 0.8,
    minMessagesForProfile: 3,
    participationScoreWeights: {
        frequency: 0.3,
        recency: 0.25,
        consistency: 0.25,
        influence: 0.2,
    },
};

/**
 * 用户活跃度跟踪器实现
 */
export class UserActivityTracker implements IUserActivityTracker {
    private userProfiles = new Map<string, Map<string, UserProfile>>(); // channelId -> userId -> UserProfile
    private channelDynamics = new Map<string, ChannelDynamics>();
    private config: UserActivityTrackerConfig;

    constructor(config: Partial<UserActivityTrackerConfig> = {}) {
        this.config = { ...DEFAULT_USER_ACTIVITY_TRACKER_CONFIG, ...config };
    }

    /**
     * 跟踪用户活跃度
     */
    async trackActivity(context: AnalysisContext): Promise<ActivityAnalysisResult> {
        const { channelId, message, timestamp } = context;
        const userId = message.sender.id;

        // 获取或创建用户画像
        const userProfile = this.getOrCreateUserProfile(channelId, userId, timestamp);

        // 更新用户活跃度
        this.updateUserActivity(userProfile, message, timestamp);

        // 更新频道动态
        const channelDynamics = this.updateChannelDynamics(channelId, timestamp);

        // 分析参与趋势
        const participationTrend = this.analyzeParticipationTrend(userProfile);

        // 计算影响力评分
        const influenceScore = this.calculateInfluenceScore(channelId, userId);

        // 预测参与度
        const engagementPrediction = await this.predictEngagement(userId, channelId);

        return {
            confidence: 0.8,
            reasoning: `User activity analysis for ${userId}`,
            timestamp,
            metadata: { userId, channelId },
            userProfile,
            channelDynamics,
            participationTrend,
            influenceScore,
            engagementPrediction,
        };
    }

    /**
     * 获取用户画像
     */
    getUserProfile(userId: string): UserProfile | null {
        // 在所有频道中查找用户
        for (const channelProfiles of this.userProfiles.values()) {
            const profile = channelProfiles.get(userId);
            if (profile) return profile;
        }
        return null;
    }

    /**
     * 获取频道动态
     */
    getChannelDynamics(channelId: string): ChannelDynamics {
        return this.channelDynamics.get(channelId) || this.createDefaultChannelDynamics();
    }

    /**
     * 预测用户参与度
     */
    async predictEngagement(userId: string, channelId: string): Promise<number> {
        const profile = this.getChannelUserProfile(channelId, userId);
        if (!profile) return 0;

        const weights = this.config.participationScoreWeights;

        // 频率因子
        const frequencyFactor = Math.min(1, profile.messageCount / 50);

        // 最近活跃度因子
        const recencyFactor = this.calculateRecencyFactor(profile.lastActiveTime);

        // 一致性因子
        const consistencyFactor = this.calculateConsistencyFactor(profile);

        // 影响力因子
        const influenceFactor = this.calculateInfluenceScore(channelId, userId);

        return (
            frequencyFactor * weights.frequency +
            recencyFactor * weights.recency +
            consistencyFactor * weights.consistency +
            influenceFactor * weights.influence
        );
    }

    /**
     * 识别影响力用户
     */
    identifyInfluencers(channelId: string): string[] {
        const channelProfiles = this.userProfiles.get(channelId);
        if (!channelProfiles) return [];

        const influencers: Array<{ userId: string; score: number }> = [];

        for (const [userId, profile] of channelProfiles) {
            const influenceScore = this.calculateInfluenceScore(channelId, userId);
            if (influenceScore > this.config.influenceThreshold) {
                influencers.push({ userId, score: influenceScore });
            }
        }

        return influencers
            .sort((a, b) => b.score - a.score)
            .slice(0, 5) // 返回前5名影响者
            .map(item => item.userId);
    }

    /**
     * 获取或创建用户画像
     */
    private getOrCreateUserProfile(channelId: string, userId: string, timestamp: Date): UserProfile {
        let channelProfiles = this.userProfiles.get(channelId);
        if (!channelProfiles) {
            channelProfiles = new Map();
            this.userProfiles.set(channelId, channelProfiles);
        }

        let profile = channelProfiles.get(userId);
        if (!profile) {
            profile = {
                userId,
                messageCount: 0,
                lastActiveTime: timestamp,
                averageInterval: 0,
                participationScore: 0,
                isActiveParticipant: false,
                interests: [],
                communicationStyle: 'passive',
            };
            channelProfiles.set(userId, profile);
        }

        return profile;
    }

    /**
     * 获取频道用户画像
     */
    private getChannelUserProfile(channelId: string, userId: string): UserProfile | null {
        const channelProfiles = this.userProfiles.get(channelId);
        return channelProfiles?.get(userId) || null;
    }

    /**
     * 更新用户活跃度
     */
    private updateUserActivity(profile: UserProfile, message: any, timestamp: Date): void {
        const timeSinceLastMessage = timestamp.getTime() - profile.lastActiveTime.getTime();

        // 更新基础统计
        profile.messageCount++;
        profile.lastActiveTime = timestamp;

        // 更新平均间隔
        if (profile.messageCount > 1) {
            profile.averageInterval =
                (profile.averageInterval * (profile.messageCount - 2) + timeSinceLastMessage) /
                (profile.messageCount - 1);
        }

        // 更新兴趣标签（基于消息内容）
        this.updateUserInterests(profile, message.content);

        // 更新交流风格
        this.updateCommunicationStyle(profile);

        // 更新参与度评分
        profile.participationScore = this.calculateParticipationScore(profile);

        // 更新活跃参与者状态
        profile.isActiveParticipant = profile.participationScore > 0.6;
    }

    /**
     * 更新用户兴趣
     */
    private updateUserInterests(profile: UserProfile, content: string): void {
        // 简化的兴趣提取逻辑
        const keywords = this.extractKeywords(content);

        // 更新兴趣列表，保持最多10个兴趣
        for (const keyword of keywords) {
            if (!profile.interests.includes(keyword)) {
                profile.interests.push(keyword);
                if (profile.interests.length > 10) {
                    profile.interests.shift();
                }
            }
        }
    }

    /**
     * 提取关键词
     */
    private extractKeywords(content: string): string[] {
        const cleanContent = content
            .toLowerCase()
            .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();

        return cleanContent
            .split(' ')
            .filter(word => word.length > 2)
            .filter(word => !this.isStopWord(word))
            .slice(0, 3);
    }

    /**
     * 停用词判断
     */
    private isStopWord(word: string): boolean {
        const stopWords = new Set([
            '的', '了', '是', '在', '我', '你', '他', '她', '它', '这', '那',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to'
        ]);
        return stopWords.has(word);
    }

    /**
     * 更新交流风格
     */
    private updateCommunicationStyle(profile: UserProfile): void {
        const avgInterval = profile.averageInterval;
        const messageCount = profile.messageCount;

        if (messageCount < this.config.minMessagesForProfile) {
            profile.communicationStyle = 'passive';
            return;
        }

        // 基于发言频率和间隔判断交流风格
        if (messageCount > 20 && avgInterval < 60000) { // 1分钟内
            profile.communicationStyle = 'active';
        } else if (messageCount > 5 && avgInterval < 300000) { // 5分钟内
            profile.communicationStyle = 'passive';
        } else {
            profile.communicationStyle = 'lurker';
        }
    }

    /**
     * 计算参与度评分
     */
    private calculateParticipationScore(profile: UserProfile): number {
        const weights = this.config.participationScoreWeights;

        // 频率评分
        const frequencyScore = Math.min(1, profile.messageCount / 50);

        // 最近活跃度评分
        const recencyScore = this.calculateRecencyFactor(profile.lastActiveTime);

        // 一致性评分
        const consistencyScore = this.calculateConsistencyFactor(profile);

        return (
            frequencyScore * weights.frequency +
            recencyScore * weights.recency +
            consistencyScore * weights.consistency
        );
    }

    /**
     * 计算最近活跃度因子
     */
    private calculateRecencyFactor(lastActiveTime: Date): number {
        const timeSinceLastActive = Date.now() - lastActiveTime.getTime();
        const decayRate = this.config.engagementDecayRate;
        const timeWindow = this.config.activityWindowMs;

        return Math.pow(decayRate, timeSinceLastActive / timeWindow);
    }

    /**
     * 计算一致性因子
     */
    private calculateConsistencyFactor(profile: UserProfile): number {
        // 基于平均间隔的一致性评估
        if (profile.messageCount < 3) return 0.5;

        const avgInterval = profile.averageInterval;
        const idealInterval = 5 * 60 * 1000; // 5分钟理想间隔

        return Math.max(0, 1 - Math.abs(avgInterval - idealInterval) / idealInterval);
    }

    /**
     * 计算影响力评分
     */
    private calculateInfluenceScore(channelId: string, userId: string): number {
        const profile = this.getChannelUserProfile(channelId, userId);
        if (!profile) return 0;

        const channelProfiles = this.userProfiles.get(channelId);
        if (!channelProfiles) return 0;

        // 消息数量相对排名
        const allMessageCounts = Array.from(channelProfiles.values()).map(p => p.messageCount);
        const userRank = allMessageCounts.filter(count => count > profile.messageCount).length;
        const rankScore = 1 - userRank / allMessageCounts.length;

        // 活跃度评分
        const activityScore = profile.participationScore;

        // 交流风格加权
        const styleWeight = profile.communicationStyle === 'active' ? 1.2 :
                           profile.communicationStyle === 'passive' ? 1.0 : 0.8;

        return Math.min(1, (rankScore * 0.6 + activityScore * 0.4) * styleWeight);
    }

    /**
     * 更新频道动态
     */
    private updateChannelDynamics(channelId: string, timestamp: Date): ChannelDynamics {
        let dynamics = this.channelDynamics.get(channelId);
        if (!dynamics) {
            dynamics = this.createDefaultChannelDynamics();
            this.channelDynamics.set(channelId, dynamics);
        }

        const channelProfiles = this.userProfiles.get(channelId);
        if (!channelProfiles) return dynamics;

        const now = timestamp.getTime();
        const activityWindow = this.config.activityWindowMs;

        // 更新参与者统计
        dynamics.totalParticipants = channelProfiles.size;
        dynamics.activeParticipants = Array.from(channelProfiles.values())
            .filter(profile => now - profile.lastActiveTime.getTime() < activityWindow)
            .length;

        // 更新对话强度
        dynamics.conversationIntensity = this.calculateConversationIntensity(channelId, timestamp);

        // 更新话题切换频率（简化实现）
        dynamics.topicSwitchFrequency = this.estimateTopicSwitchFrequency(channelId);

        // 更新平均响应时间
        dynamics.averageResponseTime = this.calculateAverageResponseTime(channelId);

        // 更新活跃时段
        dynamics.peakActivityPeriods = this.detectPeakActivityPeriods(channelId);

        return dynamics;
    }

    /**
     * 创建默认频道动态
     */
    private createDefaultChannelDynamics(): ChannelDynamics {
        return {
            totalParticipants: 0,
            activeParticipants: 0,
            conversationIntensity: 0,
            topicSwitchFrequency: 0,
            averageResponseTime: 0,
            peakActivityPeriods: [],
        };
    }

    /**
     * 计算对话强度
     */
    private calculateConversationIntensity(channelId: string, timestamp: Date): number {
        const channelProfiles = this.userProfiles.get(channelId);
        if (!channelProfiles) return 0;

        const now = timestamp.getTime();
        const timeWindow = 5 * 60 * 1000; // 5分钟窗口

        // 计算最近活跃的用户数
        const recentActiveUsers = Array.from(channelProfiles.values())
            .filter(profile => now - profile.lastActiveTime.getTime() < timeWindow)
            .length;

        // 基于活跃用户比例计算强度
        const totalUsers = channelProfiles.size;
        const intensityRatio = totalUsers > 0 ? recentActiveUsers / totalUsers : 0;

        return Math.min(1, intensityRatio * 2); // 放大系数
    }

    /**
     * 估算话题切换频率
     */
    private estimateTopicSwitchFrequency(channelId: string): number {
        // 简化实现：基于用户活跃度变化估算
        const channelProfiles = this.userProfiles.get(channelId);
        if (!channelProfiles) return 0;

        const activeUsers = Array.from(channelProfiles.values())
            .filter(profile => profile.isActiveParticipant);

        // 活跃用户越多，话题切换可能性越高
        return Math.min(1, activeUsers.length / 10);
    }

    /**
     * 计算平均响应时间
     */
    private calculateAverageResponseTime(channelId: string): number {
        const channelProfiles = this.userProfiles.get(channelId);
        if (!channelProfiles) return 0;

        const intervals = Array.from(channelProfiles.values())
            .filter(profile => profile.averageInterval > 0)
            .map(profile => profile.averageInterval);

        if (intervals.length === 0) return 0;

        return intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    }

    /**
     * 检测活跃时段
     */
    private detectPeakActivityPeriods(channelId: string): TimeRange[] {
        // 简化实现：基于当前活跃度检测
        const dynamics = this.channelDynamics.get(channelId);
        if (!dynamics || dynamics.conversationIntensity < this.config.peakDetectionThreshold) {
            return [];
        }

        const now = new Date();
        const peakStart = new Date(now.getTime() - 10 * 60 * 1000); // 10分钟前开始

        return [{
            start: peakStart,
            end: now,
            intensity: dynamics.conversationIntensity,
        }];
    }

    /**
     * 分析参与趋势
     */
    private analyzeParticipationTrend(profile: UserProfile): 'increasing' | 'stable' | 'decreasing' {
        const recencyFactor = this.calculateRecencyFactor(profile.lastActiveTime);
        const participationScore = profile.participationScore;

        // 简化的趋势分析
        if (recencyFactor > 0.8 && participationScore > 0.7) {
            return 'increasing';
        } else if (recencyFactor < 0.3 || participationScore < 0.3) {
            return 'decreasing';
        } else {
            return 'stable';
        }
    }

    /**
     * 清理过期用户数据
     */
    public cleanupExpiredUsers(): void {
        const now = Date.now();
        const timeout = this.config.userTimeoutMs;

        for (const [channelId, channelProfiles] of this.userProfiles) {
            for (const [userId, profile] of channelProfiles) {
                if (now - profile.lastActiveTime.getTime() > timeout) {
                    channelProfiles.delete(userId);
                }
            }

            // 如果频道没有用户了，删除频道
            if (channelProfiles.size === 0) {
                this.userProfiles.delete(channelId);
                this.channelDynamics.delete(channelId);
            }
        }
    }

    /**
     * 获取统计信息
     */
    public getStatistics(): {
        totalChannels: number;
        totalUsers: number;
        activeUsers: number;
        averageParticipationScore: number;
    } {
        let totalUsers = 0;
        let activeUsers = 0;
        let totalParticipationScore = 0;

        for (const channelProfiles of this.userProfiles.values()) {
            for (const profile of channelProfiles.values()) {
                totalUsers++;
                totalParticipationScore += profile.participationScore;
                if (profile.isActiveParticipant) {
                    activeUsers++;
                }
            }
        }

        return {
            totalChannels: this.userProfiles.size,
            totalUsers,
            activeUsers,
            averageParticipationScore: totalUsers > 0 ? totalParticipationScore / totalUsers : 0,
        };
    }
}
