/**
 * TopicAnalyzer - 话题分析和生命周期管理模块
 *
 * 负责话题识别、分类、生命周期管理和相似度计算
 */

import {
    ITopicAnalyzer,
    AnalysisContext,
    TopicAnalysisResult,
    TopicInfo,
    TopicUpdate,
    TopicTransition,
    IIntelligenceService
} from './interfaces';

/**
 * 话题分析器配置
 */
export interface TopicAnalyzerConfig {
    maxActiveTopics: number;
    minMessagesForStable: number;
    minParticipantsForStable: number;
    topicTimeoutMs: number;
    topicCoolingMs: number;
    similarityThreshold: number;
    keywordLimit: number;
    enableSemanticAnalysis: boolean;
    mergeThreshold: number;
}

/**
 * 默认配置
 */
export const DEFAULT_TOPIC_ANALYZER_CONFIG: TopicAnalyzerConfig = {
    maxActiveTopics: 5,
    minMessagesForStable: 5,
    minParticipantsForStable: 2,
    topicTimeoutMs: 10 * 60 * 1000, // 10分钟
    topicCoolingMs: 5 * 60 * 1000,  // 5分钟
    similarityThreshold: 0.7,
    keywordLimit: 10,
    enableSemanticAnalysis: true,
    mergeThreshold: 0.8,
};

/**
 * 话题分析器实现
 */
export class TopicAnalyzer implements ITopicAnalyzer {
    private activeTopics = new Map<string, Map<string, TopicInfo>>(); // channelId -> topicId -> TopicInfo
    private config: TopicAnalyzerConfig;
    private intelligenceService?: IIntelligenceService;

    constructor(
        config: Partial<TopicAnalyzerConfig> = {},
        intelligenceService?: IIntelligenceService
    ) {
        this.config = { ...DEFAULT_TOPIC_ANALYZER_CONFIG, ...config };
        this.intelligenceService = intelligenceService;
    }

    /**
     * 分析话题
     */
    async analyzeTopics(context: AnalysisContext): Promise<TopicAnalysisResult> {
        const { channelId, message } = context;
        const channelTopics = this.getChannelTopics(channelId);

        // 清理过期话题
        this.cleanupExpiredTopics(channelId);

        // 提取消息关键词和语义信息
        const messageAnalysis = await this.analyzeMessage(message);

        // 查找相关话题
        const relatedTopics = await this.findRelatedTopics(channelId, messageAnalysis);

        // 确定话题转换
        const transition = await this.determineTopicTransition(
            channelId,
            messageAnalysis,
            relatedTopics
        );

        // 更新或创建话题
        const updatedTopics = await this.updateTopics(channelId, transition, messageAnalysis, context);

        // 合并相似话题
        if (this.config.enableSemanticAnalysis) {
            await this.mergeSimilarTopics(channelId, this.config.mergeThreshold);
        }

        return {
            confidence: transition.confidence,
            reasoning: `Topic transition: ${transition.transitionType}`,
            timestamp: new Date(),
            metadata: { transition },
            activeTopics: Array.from(channelTopics.values()),
            newTopics: updatedTopics.newTopics,
            endedTopics: updatedTopics.endedTopics,
            topicTransitions: [transition],
            overallCoherence: this.calculateOverallCoherence(channelId),
        };
    }

    /**
     * 更新话题状态
     */
    updateTopicState(topicId: string, update: TopicUpdate): void {
        for (const [channelId, topics] of this.activeTopics) {
            const topic = topics.get(topicId);
            if (topic) {
                if (update.keywords) topic.keywords = update.keywords;
                if (update.status) topic.status = update.status;
                if (update.participants) topic.participants = update.participants;
                if (update.metadata) {
                    Object.assign(topic, update.metadata);
                }
                break;
            }
        }
    }

    /**
     * 获取活跃话题
     */
    getActiveTopics(channelId: string): TopicInfo[] {
        const channelTopics = this.activeTopics.get(channelId);
        if (!channelTopics) return [];

        return Array.from(channelTopics.values())
            .filter(topic => topic.status !== 'ended');
    }

    /**
     * 计算话题相似度
     */
    async calculateTopicSimilarity(topic1: TopicInfo, topic2: TopicInfo): Promise<number> {
        if (this.config.enableSemanticAnalysis &&
            this.intelligenceService &&
            topic1.semanticVector &&
            topic2.semanticVector) {
            // 使用语义向量计算相似度
            return this.calculateVectorSimilarity(topic1.semanticVector, topic2.semanticVector);
        }

        // 回退到关键词相似度
        return this.calculateKeywordSimilarity(topic1.keywords, topic2.keywords);
    }

    /**
     * 合并相似话题
     */
    async mergeSimilarTopics(channelId: string, threshold: number): Promise<void> {
        const topics = this.getActiveTopics(channelId);
        const toMerge: Array<[TopicInfo, TopicInfo]> = [];

        // 找出相似的话题对
        for (let i = 0; i < topics.length; i++) {
            for (let j = i + 1; j < topics.length; j++) {
                const similarity = await this.calculateTopicSimilarity(topics[i], topics[j]);
                if (similarity > threshold) {
                    toMerge.push([topics[i], topics[j]]);
                }
            }
        }

        // 执行合并
        for (const [topic1, topic2] of toMerge) {
            await this.mergeTopics(channelId, topic1, topic2);
        }
    }

    /**
     * 获取频道话题映射
     */
    private getChannelTopics(channelId: string): Map<string, TopicInfo> {
        if (!this.activeTopics.has(channelId)) {
            this.activeTopics.set(channelId, new Map());
        }
        return this.activeTopics.get(channelId)!;
    }

    /**
     * 分析消息
     */
    private async analyzeMessage(message: any): Promise<{
        keywords: string[];
        semanticVector?: number[];
        intent?: string;
        entities?: any[];
    }> {
        const content = message.content as string;

        // 基础关键词提取
        let keywords = this.extractKeywords(content);
        let semanticVector: number[] | undefined;
        let intent: string | undefined;
        let entities: any[] | undefined;

        // 智能化分析
        if (this.config.enableSemanticAnalysis && this.intelligenceService) {
            try {
                const analysis = await this.intelligenceService.analyzeSemantics(content);
                keywords = analysis.keywords.length > 0 ? analysis.keywords : keywords;
                semanticVector = analysis.embedding;
                intent = analysis.intent;
                entities = analysis.entities;
            } catch (error) {
                console.warn('Semantic analysis failed, falling back to keyword extraction:', error);
            }
        }

        return { keywords, semanticVector, intent, entities };
    }

    /**
     * 基础关键词提取
     */
    private extractKeywords(content: string): string[] {
        // 简化的关键词提取逻辑
        const cleanContent = content
            .toLowerCase()
            .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();

        const words = cleanContent
            .split(' ')
            .filter(word => word.length > 1)
            .filter(word => !this.isStopWord(word));

        return [...new Set(words)].slice(0, this.config.keywordLimit);
    }

    /**
     * 停用词判断
     */
    private isStopWord(word: string): boolean {
        const stopWords = new Set([
            '的', '了', '是', '在', '我', '你', '他', '她', '它', '这', '那',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to'
        ]);
        return stopWords.has(word);
    }

    /**
     * 查找相关话题
     */
    private async findRelatedTopics(
        channelId: string,
        messageAnalysis: any
    ): Promise<Array<{ topic: TopicInfo; similarity: number }>> {
        const topics = this.getActiveTopics(channelId);
        const relatedTopics: Array<{ topic: TopicInfo; similarity: number }> = [];

        for (const topic of topics) {
            let similarity: number;

            if (this.config.enableSemanticAnalysis &&
                messageAnalysis.semanticVector &&
                topic.semanticVector) {
                similarity = this.calculateVectorSimilarity(
                    messageAnalysis.semanticVector,
                    topic.semanticVector
                );
            } else {
                similarity = this.calculateKeywordSimilarity(
                    messageAnalysis.keywords,
                    topic.keywords
                );
            }

            if (similarity > 0.1) { // 最低相关性阈值
                relatedTopics.push({ topic, similarity });
            }
        }

        return relatedTopics.sort((a, b) => b.similarity - a.similarity);
    }

    /**
     * 确定话题转换
     */
    private async determineTopicTransition(
        channelId: string,
        messageAnalysis: any,
        relatedTopics: Array<{ topic: TopicInfo; similarity: number }>
    ): Promise<TopicTransition> {
        if (relatedTopics.length === 0) {
            return {
                toTopicId: this.generateTopicId(channelId),
                transitionType: 'new',
                confidence: 0.9
            };
        }

        const bestMatch = relatedTopics[0];

        if (bestMatch.similarity > this.config.similarityThreshold) {
            return {
                fromTopicId: bestMatch.topic.topicId,
                toTopicId: bestMatch.topic.topicId,
                transitionType: 'continuation',
                confidence: bestMatch.similarity
            };
        } else if (bestMatch.similarity > 0.3) {
            return {
                fromTopicId: bestMatch.topic.topicId,
                toTopicId: bestMatch.topic.topicId,
                transitionType: 'shift',
                confidence: bestMatch.similarity
            };
        } else {
            return {
                toTopicId: this.generateTopicId(channelId),
                transitionType: 'new',
                confidence: 0.8
            };
        }
    }

    /**
     * 生成话题ID
     */
    private generateTopicId(channelId: string): string {
        return `topic_${channelId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 计算向量相似度
     */
    private calculateVectorSimilarity(vec1: number[], vec2: number[]): number {
        if (vec1.length !== vec2.length) return 0;

        let dotProduct = 0;
        let norm1 = 0;
        let norm2 = 0;

        for (let i = 0; i < vec1.length; i++) {
            dotProduct += vec1[i] * vec2[i];
            norm1 += vec1[i] * vec1[i];
            norm2 += vec2[i] * vec2[i];
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 计算关键词相似度
     */
    private calculateKeywordSimilarity(keywords1: string[], keywords2: string[]): number {
        if (keywords1.length === 0 || keywords2.length === 0) return 0;

        const set1 = new Set(keywords1);
        const set2 = new Set(keywords2);
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);

        return intersection.size / union.size; // Jaccard相似度
    }

    /**
     * 更新话题
     */
    private async updateTopics(
        channelId: string,
        transition: TopicTransition,
        messageAnalysis: any,
        context: AnalysisContext
    ): Promise<{ newTopics: TopicInfo[]; endedTopics: string[] }> {
        const channelTopics = this.getChannelTopics(channelId);
        const newTopics: TopicInfo[] = [];
        const endedTopics: string[] = [];

        if (transition.transitionType === 'new') {
            // 创建新话题
            const newTopic: TopicInfo = {
                topicId: transition.toTopicId,
                status: 'developing',
                keywords: messageAnalysis.keywords,
                participants: new Set([context.message.sender.id]),
                dominantParticipants: [context.message.sender.id],
                messageCount: 1,
                lastActivity: context.timestamp,
                stability: 0.1,
                engagementLevel: 0.1,
                coherence: 1.0,
                semanticVector: messageAnalysis.semanticVector,
            };

            channelTopics.set(newTopic.topicId, newTopic);
            newTopics.push(newTopic);
        } else if (transition.transitionType === 'continuation' || transition.transitionType === 'shift') {
            // 更新现有话题
            const topic = channelTopics.get(transition.toTopicId);
            if (topic) {
                topic.messageCount++;
                topic.lastActivity = context.timestamp;
                topic.participants.add(context.message.sender.id);

                // 更新关键词
                const combinedKeywords = [...new Set([...topic.keywords, ...messageAnalysis.keywords])]
                    .slice(0, this.config.keywordLimit);
                topic.keywords = combinedKeywords;

                // 更新主要参与者
                this.updateDominantParticipants(topic, context.message.sender.id);

                // 更新状态
                topic.status = this.determineTopicStatus(topic);

                // 更新稳定性和参与度
                topic.stability = this.calculateTopicStability(topic);
                topic.engagementLevel = this.calculateEngagementLevel(topic);
                topic.coherence = this.calculateTopicCoherence(topic, transition);
            }
        }

        return { newTopics, endedTopics };
    }

    /**
     * 更新主要参与者
     */
    private updateDominantParticipants(topic: TopicInfo, userId: string): void {
        // 简化实现：保持最近的3个参与者
        if (!topic.dominantParticipants.includes(userId)) {
            topic.dominantParticipants.unshift(userId);
            if (topic.dominantParticipants.length > 3) {
                topic.dominantParticipants.pop();
            }
        }
    }

    /**
     * 确定话题状态
     */
    private determineTopicStatus(topic: TopicInfo): TopicInfo['status'] {
        const now = Date.now();
        const timeSinceLastActivity = now - topic.lastActivity.getTime();

        if (timeSinceLastActivity > this.config.topicTimeoutMs) {
            return 'ended';
        }

        if (timeSinceLastActivity > this.config.topicCoolingMs) {
            return 'cooling';
        }

        if (topic.messageCount >= this.config.minMessagesForStable &&
            topic.participants.size >= this.config.minParticipantsForStable) {
            return 'stable';
        }

        return 'developing';
    }

    /**
     * 计算话题稳定性
     */
    private calculateTopicStability(topic: TopicInfo): number {
        const ageBonus = Math.min(0.5, topic.messageCount / 20);
        const participantBonus = Math.min(0.3, topic.participants.size / 10);
        const timeBonus = Math.min(0.2, (Date.now() - topic.lastActivity.getTime()) / (60 * 1000) / 60);

        return Math.min(1, 0.1 + ageBonus + participantBonus + timeBonus);
    }

    /**
     * 计算参与度水平
     */
    private calculateEngagementLevel(topic: TopicInfo): number {
        const messageFrequency = topic.messageCount / Math.max(1, topic.participants.size);
        const recentActivity = Math.max(0, 1 - (Date.now() - topic.lastActivity.getTime()) / (5 * 60 * 1000));

        return Math.min(1, (messageFrequency / 10) * 0.6 + recentActivity * 0.4);
    }

    /**
     * 计算话题连贯性
     */
    private calculateTopicCoherence(topic: TopicInfo, transition: TopicTransition): number {
        let baseCoherence = topic.coherence || 0.5;

        switch (transition.transitionType) {
            case 'continuation':
                baseCoherence = Math.min(1, baseCoherence + 0.1);
                break;
            case 'shift':
                baseCoherence = Math.max(0.3, baseCoherence - 0.1);
                break;
            case 'new':
                baseCoherence = 1.0;
                break;
        }

        return baseCoherence;
    }

    /**
     * 合并话题
     */
    private async mergeTopics(channelId: string, topic1: TopicInfo, topic2: TopicInfo): Promise<void> {
        const channelTopics = this.getChannelTopics(channelId);

        // 选择较新的话题作为主话题
        const [mainTopic, mergeTopic] = topic1.lastActivity > topic2.lastActivity
            ? [topic1, topic2]
            : [topic2, topic1];

        // 合并数据
        mergeTopic.participants.forEach(p => mainTopic.participants.add(p));
        mainTopic.keywords = [...new Set([...mainTopic.keywords, ...mergeTopic.keywords])]
            .slice(0, this.config.keywordLimit);
        mainTopic.messageCount += mergeTopic.messageCount;
        mainTopic.engagementLevel = Math.max(mainTopic.engagementLevel, mergeTopic.engagementLevel);

        // 删除被合并的话题
        channelTopics.delete(mergeTopic.topicId);
    }

    /**
     * 清理过期话题
     */
    private cleanupExpiredTopics(channelId: string): void {
        const channelTopics = this.getChannelTopics(channelId);
        const now = Date.now();

        for (const [topicId, topic] of channelTopics) {
            if (now - topic.lastActivity.getTime() > this.config.topicTimeoutMs) {
                topic.status = 'ended';
                channelTopics.delete(topicId);
            }
        }
    }

    /**
     * 计算整体连贯性
     */
    private calculateOverallCoherence(channelId: string): number {
        const topics = this.getActiveTopics(channelId);
        if (topics.length === 0) return 1.0;

        const totalCoherence = topics.reduce((sum, topic) => sum + topic.coherence, 0);
        return totalCoherence / topics.length;
    }
}
